import 'dart:convert';

import 'package:animations/animations.dart'; // For PageTransitionSwitcher
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_exit_app/flutter_exit_app.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_body.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/about_us/view/about_us_screen.dart';
import 'package:quran_broadcast_app/src/features/audio_stream/view/audio_stream_screen.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/calendar/view/calendar.screen.dart';
import 'package:quran_broadcast_app/src/features/home/<USER>/home.screen.dart';
import 'package:quran_broadcast_app/src/features/main_screen/view/widgets/main_screen_top_section.widget.dart';
import 'package:quran_broadcast_app/src/features/qiblah/view/qiblah.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/models/surah_model.dart';
import 'package:quran_broadcast_app/src/features/quran/view/quran_screen/widgets/render_quran_screen.dart';
import 'package:quran_broadcast_app/src/features/settings/view/settings.screen.dart';
import 'package:quran_broadcast_app/src/features/today_wisdom/view/today_wisdom_screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/services/home_widgets_service/home_widget.service.dart';

class MainScreen extends HookConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final previousIndex = useState(currentIndex);

    useEffect(() {
      previousIndex.value = currentIndex;

      return () {};
    }, [currentIndex]);

    // useEffect(
    //   () {
    //     WidgetsBinding.instance.addPostFrameCallback(
    //       (_) {
    //         HomeWidgetService.update(context, ref: ref);
    //       },
    //     );
    //
    //     return () {};
    //   },
    // );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (_, __) async {
        if (currentIndex == 0 ||
            currentIndex == 1 ||
            currentIndex == 2 ||
            currentIndex == 3) {
          showDialog(
            context: context,
            builder: (context) {
              return Dialog(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(
                        CupertinoIcons.question_circle_fill,
                        color: ColorManager.secondaryColor,
                        size: 59,
                      ),
                      AppGaps.gap16,
                      const Text(
                        'هل تريد الخروج من التطبيق؟',
                        textAlign: TextAlign.right,
                      ),
                      AppGaps.gap16,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Expanded(
                            flex: 2,
                            child: TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: Text(
                                'إلغاء',
                                style: AppTextStyles.labelLarge
                                    .copyWith(color: ColorManager.darkGrey),
                              ),
                            ),
                          ),
                          AppGaps.gap16,
                          Expanded(
                            flex: 3,
                            child: SizedBox(
                              height: 40.h,
                              child: Button(
                                  label: 'تأكيد',
                                  onPressed: () {
                                    context.back();

                                    FlutterExitApp.exitApp();
                                    // exit(0);
                                  }),
                            ),
                          ),
                        ],
                      ),
                      AppGaps.gap4,
                    ],
                  ),
                ),
              );
            },
          );
        } else {
          calendarController.currentDateNotifier.value = DateTime.now();

          bottomNavCtrl.changeIndex(0);
        }
      },
      child: Scaffold(
        bottomNavigationBar: const BottomNavBarWidget(),
        body: BackgroundBody(
          child: Stack(
            children: [
              Assets.images.header.image(
                height: 280,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
              MainScreenTopSection(
                currentIndex: currentIndex,
              ),
              Padding(
                padding: const EdgeInsets.only(top: AppSpaces.topPadding),
                child: PageTransitionSwitcher(
                  duration: const Duration(milliseconds: 400),
                  reverse: previousIndex.value > currentIndex,
                  transitionBuilder: (child, animation, secondaryAnimation) {
                    return SharedAxisTransition(
                      animation: animation,
                      secondaryAnimation: secondaryAnimation,
                      transitionType: SharedAxisTransitionType.horizontal,
                      child: child,
                    );
                  },
                  child: BackgroundBody(
                    child: SelectedScreen(
                      key: ValueKey(currentIndex),
                      currentIndex: currentIndex,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

final suraJsonData = ValueNotifier([]);
final filteredData = ValueNotifier<List<SurahModel>>([]);

class SelectedScreen extends HookWidget {
  final int currentIndex;

  const SelectedScreen({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    List<SurahModel> filterSurahData(dynamic data) {
      List<SurahModel> surahs = [];
      for (var surah in data) {
        surahs.add(SurahModel.fromMap(surah));
      }
      return surahs;
    }

    loadJsonAsset() async {
      final String jsonString =
          await rootBundle.loadString('assets/json/surahs.json');
      var data = jsonDecode(jsonString);

      suraJsonData.value = data;

      filteredData.value = await compute(
        filterSurahData,
        suraJsonData.value,
      );
    }

    useEffect(() {
      if (suraJsonData.value.isEmpty) {
        loadJsonAsset();
      }

      return () {};
    }, []);

    return Builder(
      builder: (context) {
        switch (currentIndex) {
          case 1:
            return const AudioStreamScreen();
          case 2:
            return const RenderQuranScreen();
          case 4:
            return const QiblahScreen();
          case 5:
            return const TodayWisdomScreen();

          case 6:
            return const CalendarScreen();

          case 7:
            return const AboutUsScreen();

          default:
            return IndexedStack(
              index: currentIndex == 1 || currentIndex == 2
                  ? 0
                  : currentIndex == 3
                      ? 1
                      : currentIndex,
              children: const [
                HomeScreen(),
                SettingsScreen(),
              ],
            );
        }
      },
    );
  }
}
