import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gif/gif.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/notifications/controller/notification_controller.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_constants.dart';
import '../../../core/shared/services/home_widgets_service/home_widget.service.dart';
import '../../../core/shared/utils/clear_app_cache.dart';
import '../../audio_stream/view/audio_stream_screen.dart';
import '../../calendar/controllers/calendar.controller.dart';
import '../../main_screen/view/main.screen.dart';
import '../../settings/models/settings_model.dart';
import '../../settings/providers/settings.providers.dart';

class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerNotifierProvider);
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final notificationController =
        ref.watch(notificationControllerProvider(ref));

    final lastUpdateLocalTime = DateTime.tryParse(
      GetStorageService.getData(key: LocalKeys.lastUpdateLocalTime) ?? '',
    );
    final clearedDataDay = GetStorageService.getData(
      key: LocalKeys.clearedDataDay,
    );

    Future<void> preloadAudio() async {
      try {
        final url = settingsController.settings.value.live.url.isEmpty
            ? AppConsts.liveUrl
            : settingsController.settings.value.live.url;

        await globalAudioPlayerHandler.setUrl(url);
      } catch (e) {
        debugPrint('Error preloading audio: $e');
        hasInitializedAudio = true;
      }
    }

    void init() async {
      Future.microtask(() async {
        try {
          final localSettings = await settingsController.getSettingsFromLocal();

          final isAllowed =
              await AwesomeNotifications().isNotificationAllowed();

          const timeoutDuration = Duration(seconds: 10);

          CalendarController.calendar.value =
              await calendarController.getCalendarFromLocal();

          if (localSettings != SettingsModel.empty() &&
              CalendarController.calendar.value.isNotEmpty) {
            settingsController.getSettings().then(
              (settings) async {
                final isLastUpdateSameOfLastSavedLocalUpdateTime =
                    settings.lastUpdatedAt.formatDateToStringWithTime ==
                        lastUpdateLocalTime.formatDateToStringWithTime;

                final today = DateTime.now().formatDateToString;
                final hasToday = CalendarController.calendar.value
                    .expand((c) => c.days)
                    .any((d) => d.gregorianDate == today);

                if (!isLastUpdateSameOfLastSavedLocalUpdateTime || !hasToday) {
                  if (clearedDataDay != null &&
                      clearedDataDay == DateTime.now().formatDateToString) {
                    Log.w('Data_Was_Cleared_Today, Skipping Cache Clear');
                  } else {
                    await clearCache();
                    await clearAppFiles();

                    await GetStorageService.setData(
                        key: LocalKeys.clearedDataDay,
                        value: DateTime.now().formatDateToString);
                  }

                  await calendarController
                      .getCalendar(
                        date: DateTime.now().formatDateToString,
                        overrideLocalData: true,
                      )
                      .timeout(timeoutDuration);

                  if (isAllowed) {
                    notificationController.schedulePrayerNotifications();
                    // Schedule widget updates for all future days (only when API data changes)
                    await HomeWidgetService.scheduleAllFutureWidgetUpdates();
                  }

                  // HomeWidgetService.update(context, ref: ref);
                }
              },
            ).timeout(timeoutDuration);

            await Future.delayed(const Duration(seconds: 3));
          } else {
            await settingsController.getSettings().timeout(timeoutDuration);
            CalendarController.calendar.value = await calendarController
                .getCalendar(date: DateTime.now().formatDateToString)
                .timeout(timeoutDuration);

            if (isAllowed) {
              notificationController.schedulePrayerNotifications();
              // Schedule widget updates for all future days (only when API data changes)
              await HomeWidgetService.scheduleAllFutureWidgetUpdates();
            }

            // HomeWidgetService.update(context, ref: ref);
          }

          //! Save all prayer times for widget after calendar data is loaded
          if (Platform.isIOS) {
            HomeWidgetService.saveIOSAllPrayerTimesForWidget();
          }

          await precacheImage(
              const AssetImage('assets/images/banner.webp'), context);

          preloadAudio();

          const MainScreen().navigateReplacement;
        } catch (e, s) {
          Log.e('Splash_Error: $e, $s');
        }
      });
    }

    useEffect(() {
      init();

      return () {};
    }, []);

    return Scaffold(
      backgroundColor: ColorManager.backgroundColor,
      body: Gif(
        image: AssetImage(Assets.images.splash.path),
        height: double.infinity,
        width: double.infinity,
        fit: BoxFit.cover,
        repeat: ImageRepeat.repeat,
        autostart: Autostart.loop,
        fps: 12,
      ),
    );
  }
}
