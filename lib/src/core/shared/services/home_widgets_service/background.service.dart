import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_widget/home_widget.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/calendar/models/calendar_model.dart';
import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

/// The name associated with the UI isolate's [SendPort].
const String isolateName = 'prayer_widget_isolate';

/// Configuration constants for alarm management
class AlarmConfig {
  static const int maxConcurrentAlarms =
      2; // Only schedule next prayer + midnight
  static const int nextPrayerAlarmId = 1; // ID for next prayer alarm
  static const int midnightAlarmId = 999999; // ID for midnight refresh alarm
}

/// A port used to communicate from a background isolate to the UI isolate.
ReceivePort port = ReceivePort();

/// Initialize the background service
Future<void> initializeBackgroundService() async {
  if (Platform.isAndroid) {
    // Register the UI isolate's SendPort to allow for communication from the background isolate
    IsolateNameServer.registerPortWithName(
      port.sendPort,
      isolateName,
    );

    // Request exact alarm permission
    await Permission.scheduleExactAlarm.request();

    // Initialize Android Alarm Manager
    await AndroidAlarmManager.initialize();
  }
}

/// The background callback for prayer time updates
@pragma('vm:entry-point')
void prayerTimeUpdateCallback() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorageService.init();
  await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

  Log.i('Background task started at ${DateTime.now()}');

  final calendarController =
      ProviderContainer().read(calendarControllerNotifierProvider);

  CalendarController.calendar.value =
      await calendarController.getCalendarFromLocal();

  final now = DateTime.now();
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;
  var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

  Log.i('Current time: $now');
  Log.i('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

  if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
    final nextDay = (now.hour >= 17 && now.hour <= 23)
        ? DateTime(now.year, now.month, now.day + 1, 0, 0)
        : now;

    Log.i('Fetching next day data for Fajr: $nextDay');
    currentDayData = calendarController.calendarByDate(nextDay);
    prayerTimes = currentDayData.prayerTimes;
    nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
    Log.i(
        'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
  }

  currentDayData = calendarController.calendarByDate(now);
  prayerTimes = currentDayData.prayerTimes;

  await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);

  // Only save iOS widget data on iOS devices
  if (Platform.isIOS) {
    await HomeWidgetService.saveIOSAllPrayerTimesForWidget();
  }

  await HomeWidget.updateWidget(
      iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

  if (Platform.isAndroid) {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget4x1);
  }

  Log.i('Widget updated successfully');

  // Notify UI isolate if available
  final uiSendPort = IsolateNameServer.lookupPortByName(isolateName);
  uiSendPort?.send(null);

  // Schedule next prayer time update or midnight update
  try {
    if (Platform.isAndroid) {
      final now = DateTime.now();

      // Check current alarm count before scheduling
      final currentAlarmCount = await _estimateCurrentAlarmCount();
      Log.i('Current estimated alarm count: $currentAlarmCount');

      if (currentAlarmCount >= AlarmConfig.maxAlarmsAllowed) {
        Log.w(
            'Too many alarms scheduled ($currentAlarmCount). Skipping scheduling to prevent error.');
        return;
      }

      // If this is the last prayer of the day (Isha) or it's past 11 PM, schedule tomorrow's prayers
      if (nextPrayerTime.name == AppConsts.prayerNames[0] || now.hour >= 23) {
        await scheduleAllDailyPrayerUpdates();
      } else {
        await scheduleNextPrayerUpdate();
      }
    }
  } catch (e) {
    Log.e('Error scheduling next update: $e');

    // If we hit the alarm limit error, try to clean up and reschedule smartly
    if (e.toString().contains('Maximum limit of concurrent alarms')) {
      Log.w('Hit alarm limit. Attempting cleanup and smart rescheduling...');
      try {
        await cancelAllScheduledAlarms();
        await _scheduleSmartPrayerUpdates();
      } catch (cleanupError) {
        Log.e('Error during cleanup and rescheduling: $cleanupError');
      }
    }
  }

  Log.i('Background task completed at ${DateTime.now()}');
}

/// Schedule the next prayer time update
Future<void> scheduleNextPrayerUpdate() async {
  if (Platform.isIOS) {
    // For iOS, we rely on the widget's own timeline updates
    Log.i('iOS: Widget will handle its own timeline updates');
    return;
  }

  try {
    final calendarController =
        ProviderContainer().read(calendarControllerNotifierProvider);

    final now = DateTime.now();

    var currentDayData = calendarController.calendarByDate(now);
    var prayerTimes = currentDayData.prayerTimes;
    var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

    // Handle next day logic for Fajr
    if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
      final nextDay = (now.hour >= 17 && now.hour <= 23)
          ? DateTime(now.year, now.month, now.day + 1, 0, 0)
          : now;

      currentDayData = calendarController.calendarByDate(nextDay);
      prayerTimes = currentDayData.prayerTimes;
      nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
    }

    // The next prayer time is already a DateTime object
    final prayerDate = nextPrayerTime.time;

    const alarmId = 1; // Use a simple ID for the next prayer

    await AndroidAlarmManager.oneShotAt(
      prayerDate,
      alarmId,
      prayerTimeUpdateCallback,
      // alarmClock: true,
      exact: true,
      wakeup: true,
      rescheduleOnReboot: true,
    );

    Log.i(
        'Scheduled next prayer: ${nextPrayerTime.name} at ${prayerDate.toString()}');
  } catch (e) {
    Log.e('Error scheduling next prayer update: $e');
  }
}

/// Schedule prayer updates for a limited number of future days (smart scheduling)
Future<void> scheduleAllFuturePrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Cancel existing scheduled tasks
    await cancelAllScheduledAlarms();

    // Check current alarm count before scheduling
    final currentAlarmCount = await _estimateCurrentAlarmCount();
    Log.i('Estimated current alarm count: $currentAlarmCount');

    if (currentAlarmCount >= AlarmConfig.maxAlarmsAllowed) {
      Log.w('Too many alarms already scheduled. Skipping scheduling.');
      return;
    }

    await _scheduleSmartPrayerUpdates();
  } catch (e) {
    Log.e('Error scheduling future prayer updates: $e');
  }
}

/// Smart scheduling that limits the number of days and alarms
Future<void> _scheduleSmartPrayerUpdates() async {
  final futureDays = CalendarController.calendarBySummerTime
      .expand((calendar) => calendar.days);

  final now = DateTime.now();
  int scheduledCount = 0;
  int totalAlarmsScheduled = 0;

  // Schedule prayer times for limited future days
  for (final day in futureDays) {
    try {
      final dayDate = DateTime.parse(day.gregorianDate);

      // Only schedule future days within our limit
      if (dayDate.isAfter(now.subtract(const Duration(days: 1))) &&
          scheduledCount < AlarmConfig.maxDaysToSchedule) {
        final alarmsForDay =
            await _schedulePrayerTimesForFutureDate(day, dayDate);
        totalAlarmsScheduled += alarmsForDay;
        scheduledCount++;

        // Safety check to prevent exceeding alarm limits
        if (totalAlarmsScheduled >= AlarmConfig.maxAlarmsAllowed) {
          Log.w(
              'Approaching alarm limit. Stopping scheduling at $totalAlarmsScheduled alarms.');
          break;
        }
      }
    } catch (e) {
      Log.e('Error parsing date for day: ${day.gregorianDate} - $e');
    }
  }

  Log.i(
      'Prayer times scheduled for $scheduledCount days ($totalAlarmsScheduled alarms)');
}

/// Schedule all daily prayer updates using android_alarm_manager_plus
Future<void> scheduleAllDailyPrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Check if we need to schedule more days (rolling schedule)
    final needsRescheduling = await _shouldRescheduleAlarms();

    if (needsRescheduling) {
      Log.i('Rolling schedule triggered - scheduling next batch of days');
      await _scheduleSmartPrayerUpdates();
      return;
    }

    final calendarController =
        ProviderContainer().read(calendarControllerNotifierProvider);

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    // Only cancel and reschedule if we have few alarms left
    final currentAlarmCount = await _estimateCurrentAlarmCount();
    if (currentAlarmCount < 20) {
      await cancelAllScheduledAlarms();

      // Schedule prayer times for today (if any remaining)
      await _schedulePrayerTimesForDate(calendarController, today, 'today');

      // Schedule prayer times for tomorrow
      await _schedulePrayerTimesForDate(
          calendarController, tomorrow, 'tomorrow');

      // Schedule midnight update for the day after tomorrow
      final dayAfterTomorrow = tomorrow.add(const Duration(days: 1));
      final midnightUpdate = DateTime(dayAfterTomorrow.year,
          dayAfterTomorrow.month, dayAfterTomorrow.day, 0, 0);
      await _scheduleAlarmAt(
          999999, // Special ID for midnight refresh
          midnightUpdate,
          prayerTimeUpdateCallback);

      Log.i('Prayer times scheduled successfully for today and tomorrow');
    } else {
      Log.i(
          'Sufficient alarms already scheduled ($currentAlarmCount). Skipping daily scheduling.');
    }
  } catch (e) {
    Log.e('Error scheduling daily prayer updates: $e');
  }
}

/// Schedule prayer times for a specific day using android_alarm_manager_plus
Future<void> _schedulePrayerTimesForDate(CalendarController calendarController,
    DateTime date, String dayLabel) async {
  try {
    final dayData = calendarController.calendarByDate(date);
    final prayerTimes = dayData.prayerTimes;
    final now = DateTime.now();

    // List of prayer times to schedule
    final prayers = [
      {'name': 'fajr', 'time': prayerTimes.fajr},
      {'name': 'sunrise', 'time': prayerTimes.sunrise},
      {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
      {'name': 'asr', 'time': prayerTimes.asr},
      {'name': 'maghrib', 'time': prayerTimes.maghrib},
      {'name': 'isha', 'time': prayerTimes.isha},
    ];

    for (final prayer in prayers) {
      try {
        final timeString = prayer['time'] as String;
        if (timeString.isEmpty) continue;

        final timeParts = timeString.split(':');
        if (timeParts.length < 2) continue;

        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        final prayerDateTime =
            DateTime(date.year, date.month, date.day, hour, minute);

        // Only schedule if the prayer time is in the future
        if (prayerDateTime.isAfter(now)) {
          final alarmId = _createAlarmId(date, prayers.indexOf(prayer));
          await _scheduleAlarmAt(
              alarmId, prayerDateTime, prayerTimeUpdateCallback);
          Log.i(
              'Scheduled ${prayer['name']} for $dayLabel at ${prayer['time']}');
        }
      } catch (e) {
        Log.e(
            'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
      }
    }
  } catch (e) {
    Log.e('Error scheduling prayer times for $dayLabel: $e');
  }
}

/// Schedule prayer times for a future day (used by scheduleAllFuturePrayerUpdates)
Future<int> _schedulePrayerTimesForFutureDate(
    DayModel day, DateTime dayDate) async {
  int alarmsScheduled = 0;

  try {
    final prayerTimes = day.prayerTimes;
    final now = DateTime.now();

    // List of prayer times to schedule
    final prayers = [
      {'name': 'fajr', 'time': prayerTimes.fajr},
      {'name': 'sunrise', 'time': prayerTimes.sunrise},
      {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
      {'name': 'asr', 'time': prayerTimes.asr},
      {'name': 'maghrib', 'time': prayerTimes.maghrib},
      {'name': 'isha', 'time': prayerTimes.isha},
    ];

    for (final prayer in prayers) {
      try {
        final timeString = prayer['time'] as String;
        if (timeString.isEmpty) continue;

        final timeParts = timeString.split(':');
        if (timeParts.length < 2) continue;

        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        final prayerDateTime =
            DateTime(dayDate.year, dayDate.month, dayDate.day, hour, minute);

        // Only schedule if the prayer time is in the future
        if (prayerDateTime.isAfter(now)) {
          final alarmId = _createAlarmId(dayDate, prayers.indexOf(prayer));
          await _scheduleAlarmAt(
              alarmId, prayerDateTime, prayerTimeUpdateCallback);
          alarmsScheduled++;
          Log.i(
              'Scheduled ${prayer['name']} for ${dayDate.toString().split(' ')[0]} at ${prayer['time']}');
        }
      } catch (e) {
        Log.e(
            'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
      }
    }
  } catch (e) {
    Log.e('Error scheduling prayer times for future date: $e');
  }

  return alarmsScheduled;
}

/// Schedule an alarm at a specific time using android_alarm_manager_plus
Future<void> _scheduleAlarmAt(
    int alarmId, DateTime scheduledTime, Function callback) async {
  try {
    await AndroidAlarmManager.oneShotAt(
      scheduledTime,
      alarmId,
      callback,
      exact: true,
      wakeup: true,
      alarmClock: true,
      rescheduleOnReboot: true,
    );
    Log.i('Scheduled alarm $alarmId for ${scheduledTime.toString()}');
  } catch (e) {
    Log.e('Error scheduling alarm $alarmId: $e');

    // If we hit the alarm limit, throw a more specific error
    if (e.toString().contains('Maximum limit of concurrent alarms')) {
      throw Exception('Alarm limit reached: ${e.toString()}');
    }
    rethrow;
  }
}

/// Create a unique alarm ID based on date and prayer index
int _createAlarmId(DateTime date, int prayerIndex) {
  // Create unique ID: YYYYMMDDHH where HH is prayer index (0-5)
  final year = date.year % 100; // Last 2 digits of year
  final month = date.month;
  final day = date.day;

  // Format: YYMMDDHH where HH is prayer index
  return (year * 1000000) + (month * 10000) + (day * 100) + prayerIndex;
}

/// Cancel all scheduled alarms
Future<void> cancelAllScheduledAlarms() async {
  if (Platform.isAndroid) {
    try {
      // Cancel the midnight refresh alarm
      await AndroidAlarmManager.cancel(999999);

      // Cancel prayer time alarms for extended range to ensure cleanup
      final now = DateTime.now();
      for (int dayOffset = -7;
          dayOffset < AlarmConfig.maxDaysToSchedule + 7;
          dayOffset++) {
        final date = now.add(Duration(days: dayOffset));
        for (int prayerIndex = 0;
            prayerIndex < AlarmConfig.prayersPerDay;
            prayerIndex++) {
          final alarmId = _createAlarmId(date, prayerIndex);
          await AndroidAlarmManager.cancel(alarmId);
        }
      }

      Log.i(
          'All scheduled alarms cancelled (${AlarmConfig.maxDaysToSchedule + 14} days range)');
    } catch (e) {
      Log.e('Error cancelling alarms: $e');
    }
  }
}

/// Estimate the current number of scheduled alarms
Future<int> _estimateCurrentAlarmCount() async {
  // Since Android doesn't provide a direct way to count alarms,
  // we estimate based on our scheduling pattern
  final now = DateTime.now();
  int estimatedCount = 0;

  // Check for alarms in the next week
  for (int dayOffset = 0;
      dayOffset < AlarmConfig.maxDaysToSchedule;
      dayOffset++) {
    final date = now.add(Duration(days: dayOffset));

    // Estimate 6 prayers per day for future days, fewer for today
    if (dayOffset == 0) {
      // For today, estimate remaining prayers
      final currentHour = now.hour;
      if (currentHour < 5)
        estimatedCount += 6; // All prayers remaining
      else if (currentHour < 6)
        estimatedCount += 5; // After Fajr
      else if (currentHour < 12)
        estimatedCount += 4; // After Sunrise
      else if (currentHour < 15)
        estimatedCount += 3; // After Dhuhr
      else if (currentHour < 18)
        estimatedCount += 2; // After Asr
      else if (currentHour < 20) estimatedCount += 1; // After Maghrib
      // After Isha: 0 remaining
    } else {
      estimatedCount += AlarmConfig.prayersPerDay;
    }
  }

  // Add 1 for midnight refresh alarm
  estimatedCount += 1;

  return estimatedCount;
}

/// Check if we should reschedule alarms (rolling schedule logic)
Future<bool> _shouldRescheduleAlarms() async {
  final now = DateTime.now();
  final futureDays = CalendarController.calendarBySummerTime
      .expand((calendar) => calendar.days);

  int futureDaysWithAlarms = 0;

  // Count how many future days we have alarms scheduled for
  for (final day in futureDays) {
    try {
      final dayDate = DateTime.parse(day.gregorianDate);

      if (dayDate.isAfter(now) &&
          dayDate.isBefore(
              now.add(Duration(days: AlarmConfig.maxDaysToSchedule)))) {
        futureDaysWithAlarms++;
      }
    } catch (e) {
      // Skip invalid dates
      continue;
    }
  }

  // If we have less than the threshold days scheduled, we need to reschedule
  return futureDaysWithAlarms <= AlarmConfig.rescheduleThreshold;
}
