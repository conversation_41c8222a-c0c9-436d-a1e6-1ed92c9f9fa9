import 'dart:io';

import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/app.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/initialize.dart';
import 'package:xr_helper/xr_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await initialize();

    // Initialize Android Alarm Manager Plus for Android
    if (Platform.isAndroid) {
      try {
        await AndroidAlarmManager.initialize();
      } catch (e) {
        Log.e('Android Alarm_Manager initialization error: $e');
      }
    }
  } catch (e) {
    Log.e('Initialization Error: $e');
  }

  runApp(
    const ProviderScope(
      child: BaseApp(),
    ),
  );
}
